'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function DashboardPage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!user && !loading) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null // Se redirigirá al login
  }

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 py-6 sm:px-0">
        <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Dashboard - Hotel Management
          </h1>
          
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Bienvenido, {user.email}!
            </h2>
            <p className="text-gray-600">
              Este es tu panel de control para gestionar las habitaciones del hotel.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-500 text-white p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Habitaciones Totales</h3>
              <p className="text-3xl font-bold">0</p>
              <p className="text-blue-100">Próximamente...</p>
            </div>
            
            <div className="bg-green-500 text-white p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Habitaciones Disponibles</h3>
              <p className="text-3xl font-bold">0</p>
              <p className="text-green-100">Próximamente...</p>
            </div>
            
            <div className="bg-yellow-500 text-white p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Reservas Activas</h3>
              <p className="text-3xl font-bold">0</p>
              <p className="text-yellow-100">Próximamente...</p>
            </div>
          </div>

          <div className="mt-8 bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Próximos pasos
            </h3>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Configurar tu proyecto de Supabase</li>
              <li>Habilitar autenticación con Google</li>
              <li>Crear las tablas de la base de datos</li>
              <li>Implementar gestión de habitaciones</li>
              <li>Implementar sistema de reservas</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

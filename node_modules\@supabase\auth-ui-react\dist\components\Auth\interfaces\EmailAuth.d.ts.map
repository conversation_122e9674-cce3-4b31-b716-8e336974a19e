{"version": 3, "file": "EmailAuth.d.ts", "sourceRoot": "", "sources": ["EmailAuth.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAsC,MAAM,OAAO,CAAA;AAC1D,OAAO,EACL,aAAa,EACb,UAAU,EACV,UAAU,EACV,UAAU,EAGX,MAAM,0BAA0B,CAAA;AACjC,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAA;AAU7C,MAAM,WAAW,cAAc;IAC7B,QAAQ,CAAC,EAAE,UAAU,GAAG,UAAU,CAAA;IAClC,YAAY,CAAC,EAAE,MAAM,CAAA;IACrB,eAAe,CAAC,EAAE,MAAM,CAAA;IACxB,WAAW,CAAC,EAAE,GAAG,CAAA;IACjB,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAA;IACzC,kBAAkB,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAA;IAC/C,cAAc,EAAE,cAAc,CAAA;IAC9B,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB,UAAU,CAAC,EAAE,UAAU,CAAA;IACvB,cAAc,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAA;IACvC,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB,IAAI,CAAC,EAAE,aAAa,CAAA;IACpB,UAAU,CAAC,EAAE,UAAU,CAAA;IACvB,QAAQ,CAAC,EAAE,KAAK,CAAC,SAAS,CAAA;CAC3B;AAED,iBAAS,SAAS,CAAC,EACjB,QAAoB,EACpB,YAAiB,EACjB,eAAoB,EACpB,WAAsB,EACtB,eAA+B,EAC/B,kBAAqC,EACrC,cAAc,EACd,SAAiB,EACjB,UAAU,EACV,cAAc,EACd,SAAS,EACT,IAAI,EACJ,UAAU,EACV,QAAQ,GACT,EAAE,cAAc,2CAyLhB;AAED,OAAO,EAAE,SAAS,EAAE,CAAA"}
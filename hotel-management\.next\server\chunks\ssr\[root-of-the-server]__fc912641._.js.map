{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Escritorio/JYC/hotel-management/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Tipos para la base de datos\nexport interface Room {\n  id: string\n  room_number: string\n  room_type: string\n  price_per_night: number\n  is_available: boolean\n  description?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Booking {\n  id: string\n  room_id: string\n  user_id: string\n  check_in_date: string\n  check_out_date: string\n  total_price: number\n  status: 'pending' | 'confirmed' | 'cancelled'\n  created_at: string\n  updated_at: string\n}\n\nexport interface Profile {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  role: 'guest' | 'admin'\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,aAAa,aAAa", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Escritorio/JYC/hotel-management/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType>({\n  user: null,\n  session: null,\n  loading: true,\n  signOut: async () => {},\n})\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext)\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Obtener sesión inicial\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setSession(session)\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Escuchar cambios de autenticación\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setSession(session)\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAaA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmB;IACjD,MAAM;IACN,SAAS;IACT,SAAS;IACT,SAAS,WAAa;AACxB;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAEA;QAEA,oCAAoC;QACpC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Escritorio/JYC/hotel-management/src/components/Navigation/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport Link from 'next/link'\n\nexport default function Navbar() {\n  const { user, signOut } = useAuth()\n\n  return (\n    <nav className=\"bg-blue-600 text-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-xl font-bold\">\n              Hotel Management\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {user ? (\n              <>\n                <Link\n                  href=\"/dashboard\"\n                  className=\"hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Dashboard\n                </Link>\n                <Link\n                  href=\"/rooms\"\n                  className=\"hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Habitaciones\n                </Link>\n                <Link\n                  href=\"/bookings\"\n                  className=\"hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Reservas\n                </Link>\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm\">\n                    {user.email}\n                  </span>\n                  <button\n                    onClick={signOut}\n                    className=\"bg-red-500 hover:bg-red-600 px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Cerrar Sesión\n                  </button>\n                </div>\n              </>\n            ) : (\n              <Link\n                href=\"/login\"\n                className=\"bg-blue-500 hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Iniciar Sesión\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAoB;;;;;;;;;;;kCAK/C,8OAAC;wBAAI,WAAU;kCACZ,qBACC;;8CACE,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;yDAML,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}
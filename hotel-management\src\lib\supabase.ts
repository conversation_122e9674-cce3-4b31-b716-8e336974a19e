import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Tipos para la base de datos
export interface Room {
  id: string
  room_number: string
  room_type: string
  price_per_night: number
  is_available: boolean
  description?: string
  created_at: string
  updated_at: string
}

export interface Booking {
  id: string
  room_id: string
  user_id: string
  check_in_date: string
  check_out_date: string
  total_price: number
  status: 'pending' | 'confirmed' | 'cancelled'
  created_at: string
  updated_at: string
}

export interface Profile {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  role: 'guest' | 'admin'
  created_at: string
  updated_at: string
}

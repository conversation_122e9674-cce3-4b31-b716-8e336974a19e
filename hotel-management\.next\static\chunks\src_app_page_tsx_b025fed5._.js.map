{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Escritorio/JYC/hotel-management/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user } = useAuth()\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Hotel Management System\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 text-blue-100\">\n              Gestiona las habitaciones de tu hotel de manera eficiente\n            </p>\n            <div className=\"space-x-4\">\n              {user ? (\n                <Link\n                  href=\"/dashboard\"\n                  className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n                >\n                  Ir al Dashboard\n                </Link>\n              ) : (\n                <>\n                  <Link\n                    href=\"/login\"\n                    className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n                  >\n                    Iniciar Sesión\n                  </Link>\n                  <Link\n                    href=\"/login\"\n                    className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\"\n                  >\n                    Registrarse\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Características Principales\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              Todo lo que necesitas para gestionar tu hotel\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center p-6\">\n              <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                Gestión de Habitaciones\n              </h3>\n              <p className=\"text-gray-600\">\n                Administra todas las habitaciones de tu hotel con facilidad\n              </p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                Sistema de Reservas\n              </h3>\n              <p className=\"text-gray-600\">\n                Gestiona las reservas de manera eficiente y organizada\n              </p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                Autenticación Segura\n              </h3>\n              <p className=\"text-gray-600\">\n                Login seguro con Google y gestión de usuarios\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;0CACZ,qBACC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;yDAID;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CA<PERSON>,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C;GAvGwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}